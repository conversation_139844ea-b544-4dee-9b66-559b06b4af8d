# Copyright (c) 2025, <PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.utils import flt, getdate, formatdate


def execute(filters=None):
	"""
	Main execution function for Steel Report

	Args:
		filters (dict): Report filters containing project, calculate_bundlewise, from_date, to_date

	Returns:
		tuple: (columns, data) for the report
	"""
	if not filters:
		filters = {}

	# Validate mandatory filters
	if not filters.get("project"):
		frappe.throw(_("Project is mandatory"))

	# Get columns definition
	columns = get_columns(filters)

	# Get report data
	data = get_steel_data(filters)

	return columns, data


def get_columns(filters=None):
	"""
	Define columns for the Steel Report

	Args:
		filters (dict): Report filters to determine which columns to show

	Returns:
		list: Column definitions with formatting
	"""
	calculate_bundlewise = filters.get("calculate_bundlewise", 0)

	columns = [
		{
			"fieldname": "date",
			"label": _("Date"),
			"fieldtype": "Date",
			"width": 100
		},
		{
			"fieldname": "supplier",
			"label": _("Supplier"),
			"fieldtype": "Link",
			"options": "Suppliers",
			"width": 150
		},
		{
			"fieldname": "challan_no",
			"label": _("Challan No"),
			"fieldtype": "Data",
			"width": 120
		}
	]

	# Add steel size columns based on calculate_bundlewise setting
	steel_sizes = ["8_mm", "10_mm", "12_mm", "16_mm", "20_mm", "25_mm", "32_mm"]

	for size in steel_sizes:
		if calculate_bundlewise:
			# Show bundle columns
			columns.append({
				"fieldname": f"{size}_b",
				"label": _(f"{size.replace('_', ' ')} (Bundles)"),
				"fieldtype": "Float",
				"width": 100,
				"precision": 2
			})
		else:
			# Show MT columns
			columns.append({
				"fieldname": size,
				"label": _(f"{size.replace('_', ' ')} (MT)"),
				"fieldtype": "Float",
				"width": 100,
				"precision": 2
			})

	# Add remarks column
	columns.append({
		"fieldname": "remarks",
		"label": _("Remarks"),
		"fieldtype": "Small Text",
		"width": 200
	})

	return columns


def get_steel_data(filters):
	"""
	Get steel register data based on filters

	Args:
		filters (dict): Report filters

	Returns:
		list: Steel register entries
	"""
	conditions = []
	values = {}

	# Project filter (mandatory)
	conditions.append("project = %(project)s")
	values["project"] = filters.get("project")

	# Date filters
	if filters.get("from_date"):
		conditions.append("date >= %(from_date)s")
		values["from_date"] = filters.get("from_date")

	if filters.get("to_date"):
		conditions.append("date <= %(to_date)s")
		values["to_date"] = filters.get("to_date")

	# Build WHERE clause
	where_clause = ""
	if conditions:
		where_clause = "WHERE " + " AND ".join(conditions)

	# Determine which columns to select based on calculate_bundlewise
	calculate_bundlewise = filters.get("calculate_bundlewise", 0)

	if calculate_bundlewise:
		# Select bundle columns
		steel_columns = "8_mm_b, 10_mm_b, 12_mm_b, 16_mm_b, 20_mm_b, 25_mm_b, 32_mm_b"
	else:
		# Select MT columns
		steel_columns = "8_mm, 10_mm, 12_mm, 16_mm, 20_mm, 25_mm, 32_mm"

	query = f"""
		SELECT
			date,
			supplier,
			challan_no,
			{steel_columns},
			remarks
		FROM
			`tabSteel Register`
		{where_clause}
		ORDER BY
			date DESC, creation DESC
	"""

	data = frappe.db.sql(query, values, as_dict=True)

	return data
